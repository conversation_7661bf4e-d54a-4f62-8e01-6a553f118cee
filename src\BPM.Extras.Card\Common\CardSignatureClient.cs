﻿using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace BPM.Extras.Card.Common;

/// <summary>
/// 签名类
/// </summary>
public class CardSignatureClient
{
    /// <summary>
    /// 生成签名的逻辑（与第三方服务完全一致）
    /// </summary>
    /// <param name="parameters">签名参数</param>
    /// <param name="signKey"></param>
    /// <param name="timestamp"></param>
    /// <returns></returns>
    public static string GenerateSignature(string parameters, string signKey, string timestamp)
    {
        var param = JsonSerializer.Deserialize<Dictionary<string, object>>(parameters);
        param.Add("timestamp", timestamp);

        // 使用与第三方服务完全一致的处理方式
        // 1. 先使用 GetSortObject 预处理参数（与第三方服务一致）
        var sortedParam = GetSortObject(param, false);
        // 2. 再使用 SortJoin 进行排序和拼接
        string unSignParaString = SortJoin((Dictionary<string, object>)sortedParam, "&", "=", false);
        // 3. 直接拼接 signKey（不使用 "&key=" 前缀）
        string dataToSign = unSignParaString + signKey;
        // 4. MD5 加密并转大写（使用与第三方服务一致的Md5By32方法）
        return Md5By32(dataToSign).ToUpper();
    }

    /// <summary>
    /// 对 JSON 对象或数组进行递归排序，忽略空值（与第三方服务完全一致）
    /// </summary>
    /// <param name="obj">需要排序的 JSON 对象或数组</param>
    /// <param name="ignoreNull">是否忽略空值</param>
    /// <returns>排序后的 JSON 对象或数组</returns>
    private static dynamic GetSortObject(object obj, bool ignoreNull)
    {
        // 处理数组
        if (obj is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.Array)
        {
            var list = jsonElement.EnumerateArray()
                .Select(item => GetSortObject(item, ignoreNull))
                .ToList();
            return list;
        }

        // 处理对象
        if (obj is JsonElement jsonObj && jsonObj.ValueKind == JsonValueKind.Object)
        {
            var sortedDict = jsonObj.EnumerateObject()
                .Where(p => !ignoreNull || !(p.Value.ValueKind == JsonValueKind.Null || (p.Value.ValueKind == JsonValueKind.String && p.Value.GetString() == "")))
                .OrderBy(p => p.Name, StringComparer.Ordinal) // 按字母顺序排序键
                .ToDictionary(p => p.Name, p => GetSortObject(p.Value, ignoreNull));

            return sortedDict;
        }

        // 处理 Dictionary<string, object>
        if (obj is Dictionary<string, object> dict)
        {
            var sortedDict = dict
                .Where(p => !ignoreNull || p.Value != null)
                .OrderBy(p => p.Key, StringComparer.Ordinal)
                .ToDictionary(p => p.Key, p => GetSortObject(p.Value, ignoreNull));

            return sortedDict;
        }

        return obj;
    }

    /// <summary>
    /// 将字典按键排序后，拼接为字符串（与第三方服务完全一致）
    /// </summary>
    /// <param name="param">需要排序并拼接的字典</param>
    /// <param name="separator">键值对之间的分隔符</param>
    /// <param name="keyValueSeparator">键和值之间的分隔符</param>
    /// <param name="ignoreNull">是否忽略空值</param>
    /// <returns>拼接后的字符串</returns>
    private static string SortJoin(Dictionary<string, object> param, string separator, string keyValueSeparator, bool ignoreNull)
    {
        return string.Join(separator, param
            .Where(p => !ignoreNull || p.Value != null)
            .OrderBy(p => p.Key, StringComparer.Ordinal)
            .Select(p => $"{p.Key}{keyValueSeparator}{(p.Value is string || p.Value is JsonElement ? p.Value : JsonSerializer.Serialize(p.Value))}"));
    }

    /// <summary>
    /// 32位MD5加密 - 与第三方服务完全一致
    /// </summary>
    /// <param name="value">输入字符串</param>
    /// <returns></returns>
    private static string Md5By32(string value)
    {
        return Md5By32(value, Encoding.UTF8);
    }

    /// <summary>
    /// 32位MD5加密 - 与第三方服务完全一致
    /// </summary>
    /// <param name="value">输入字符串</param>
    /// <param name="encoding">编码方式</param>
    /// <returns></returns>
    private static string Md5By32(string value, Encoding encoding)
    {
        return Md5(value, encoding);
    }

    /// <summary>
    /// MD5加密核心方法 - 与第三方服务完全一致
    /// </summary>
    /// <param name="value">输入字符串</param>
    /// <param name="encoding">编码方式</param>
    /// <returns></returns>
    private static string Md5(string value, Encoding encoding)
    {
        using (var md5 = MD5.Create())
        {
            var inputBytes = encoding.GetBytes(value);
            var hashBytes = md5.ComputeHash(inputBytes);
            return string.Concat(hashBytes.Select(b => b.ToString("x2")));
        }
    }

    /// <summary>
    /// HTTP POST 请求方法
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="parameters">请求</param>
    /// <param name="signKey">签名</param>
    /// <returns></returns>
    public static async Task<string> PostRequest(string url, Dictionary<string, object> dict, string signKey, string token)
    {
        string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        string sign = GenerateSignature(JsonSerializer.Serialize(dict), signKey, timestamp);
        // 将签名和时间戳加入到URL查询参数中
        string urlWithParams = $"{url}?timestamp={timestamp}&sign={sign}";
        return await BPM.Helpers.Web.Client().Post(urlWithParams).BearerToken(token).JsonData(dict).ResultAsync();
    }
}
